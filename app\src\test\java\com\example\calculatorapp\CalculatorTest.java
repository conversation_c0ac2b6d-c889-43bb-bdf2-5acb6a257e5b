package com.example.calculatorapp;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class CalculatorTest {
    
    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
    }
    
    @Test
    public void subtraction_isCorrect() {
        assertEquals(2, 4 - 2);
    }
    
    @Test
    public void multiplication_isCorrect() {
        assertEquals(8, 4 * 2);
    }
    
    @Test
    public void division_isCorrect() {
        assertEquals(2, 4 / 2);
    }
    
    @Test
    public void rollNumberSum_isCorrect() {
        // Test with sample roll number "12345"
        String rollNumber = "12345";
        int expectedSum = 1 + 2 + 3 + 4 + 5; // = 15
        
        int actualSum = 0;
        for (int i = 0; i < Math.min(5, rollNumber.length()); i++) {
            char digit = rollNumber.charAt(i);
            if (Character.isDigit(digit)) {
                actualSum += Character.getNumericValue(digit);
            }
        }
        
        assertEquals(expectedSum, actualSum);
    }
}
