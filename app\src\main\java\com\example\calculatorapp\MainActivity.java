package com.example.calculatorapp;

import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import java.text.DecimalFormat;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    private TextView tvDisplay;
    private TextView tvStudentName;
    
    private String currentInput = "";
    private String operator = "";
    private double firstOperand = 0;
    private boolean isOperatorPressed = false;
    private boolean isEqualsPressed = false;
    
    // Replace with your actual roll number digits
    private final String ROLL_NUMBER = "12345"; // Change this to your roll number
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        setClickListeners();
        
        // Set your name here
        tvStudentName.setText("Your Name Here"); // Replace with your actual name
        tvDisplay.setText("0");
    }
    
    private void initializeViews() {
        tvDisplay = findViewById(R.id.tvDisplay);
        tvStudentName = findViewById(R.id.tvStudentName);
    }
    
    private void setClickListeners() {
        // Number buttons
        findViewById(R.id.btn0).setOnClickListener(this);
        findViewById(R.id.btn1).setOnClickListener(this);
        findViewById(R.id.btn2).setOnClickListener(this);
        findViewById(R.id.btn3).setOnClickListener(this);
        findViewById(R.id.btn4).setOnClickListener(this);
        findViewById(R.id.btn5).setOnClickListener(this);
        findViewById(R.id.btn6).setOnClickListener(this);
        findViewById(R.id.btn7).setOnClickListener(this);
        findViewById(R.id.btn8).setOnClickListener(this);
        findViewById(R.id.btn9).setOnClickListener(this);
        
        // Operator buttons
        findViewById(R.id.btnPlus).setOnClickListener(this);
        findViewById(R.id.btnMinus).setOnClickListener(this);
        findViewById(R.id.btnMultiply).setOnClickListener(this);
        findViewById(R.id.btnDivide).setOnClickListener(this);
        
        // Special buttons
        findViewById(R.id.btnEquals).setOnClickListener(this);
        findViewById(R.id.btnClear).setOnClickListener(this);
        findViewById(R.id.btnBackspace).setOnClickListener(this);
        findViewById(R.id.btnDecimal).setOnClickListener(this);
        findViewById(R.id.btnMyR).setOnClickListener(this);
    }
    
    @Override
    public void onClick(View v) {
        Button button = (Button) v;
        String buttonText = button.getText().toString();
        
        int id = v.getId();
        
        if (id == R.id.btn0 || id == R.id.btn1 || id == R.id.btn2 || id == R.id.btn3 ||
            id == R.id.btn4 || id == R.id.btn5 || id == R.id.btn6 || id == R.id.btn7 ||
            id == R.id.btn8 || id == R.id.btn9) {
            handleNumberInput(buttonText);
        } else if (id == R.id.btnPlus || id == R.id.btnMinus || id == R.id.btnMultiply || id == R.id.btnDivide) {
            handleOperatorInput(buttonText);
        } else if (id == R.id.btnEquals) {
            handleEqualsInput();
        } else if (id == R.id.btnClear) {
            handleClearInput();
        } else if (id == R.id.btnBackspace) {
            handleBackspaceInput();
        } else if (id == R.id.btnDecimal) {
            handleDecimalInput();
        } else if (id == R.id.btnMyR) {
            handleMyRInput();
        }
    }
    
    private void handleNumberInput(String number) {
        if (isEqualsPressed) {
            currentInput = "";
            isEqualsPressed = false;
        }
        
        if (isOperatorPressed) {
            currentInput = "";
            isOperatorPressed = false;
        }
        
        if (currentInput.equals("0")) {
            currentInput = number;
        } else {
            currentInput += number;
        }
        
        tvDisplay.setText(currentInput);
    }
    
    private void handleOperatorInput(String op) {
        if (!currentInput.isEmpty()) {
            if (!operator.isEmpty() && !isOperatorPressed) {
                calculateResult();
            } else {
                firstOperand = Double.parseDouble(currentInput);
            }
        }
        
        operator = convertOperatorSymbol(op);
        isOperatorPressed = true;
        isEqualsPressed = false;
    }
    
    private String convertOperatorSymbol(String symbol) {
        switch (symbol) {
            case "×": return "*";
            case "÷": return "/";
            default: return symbol;
        }
    }
    
    private void handleEqualsInput() {
        if (!operator.isEmpty() && !currentInput.isEmpty() && !isOperatorPressed) {
            calculateResult();
            operator = "";
            isEqualsPressed = true;
        }
    }
    
    private void calculateResult() {
        try {
            double secondOperand = Double.parseDouble(currentInput);
            double result = 0;
            
            switch (operator) {
                case "+":
                    result = firstOperand + secondOperand;
                    break;
                case "-":
                    result = firstOperand - secondOperand;
                    break;
                case "*":
                    result = firstOperand * secondOperand;
                    break;
                case "/":
                    if (secondOperand != 0) {
                        result = firstOperand / secondOperand;
                    } else {
                        tvDisplay.setText("Error");
                        return;
                    }
                    break;
            }
            
            DecimalFormat df = new DecimalFormat("#.##########");
            currentInput = df.format(result);
            tvDisplay.setText(currentInput);
            firstOperand = result;
            
        } catch (NumberFormatException e) {
            tvDisplay.setText("Error");
        }
    }
    
    private void handleClearInput() {
        currentInput = "";
        operator = "";
        firstOperand = 0;
        isOperatorPressed = false;
        isEqualsPressed = false;
        tvDisplay.setText("0");
    }
    
    private void handleBackspaceInput() {
        if (!currentInput.isEmpty() && !isOperatorPressed) {
            currentInput = currentInput.substring(0, currentInput.length() - 1);
            if (currentInput.isEmpty()) {
                currentInput = "0";
            }
            tvDisplay.setText(currentInput);
        }
    }
    
    private void handleDecimalInput() {
        if (isEqualsPressed) {
            currentInput = "0";
            isEqualsPressed = false;
        }
        
        if (isOperatorPressed) {
            currentInput = "0";
            isOperatorPressed = false;
        }
        
        if (!currentInput.contains(".")) {
            if (currentInput.isEmpty()) {
                currentInput = "0.";
            } else {
                currentInput += ".";
            }
            tvDisplay.setText(currentInput);
        }
    }
    
    private void handleMyRInput() {
        // Calculate sum of 5 digits from roll number
        int sum = 0;
        StringBuilder rollDigits = new StringBuilder();

        for (int i = 0; i < Math.min(5, ROLL_NUMBER.length()); i++) {
            char digit = ROLL_NUMBER.charAt(i);
            if (Character.isDigit(digit)) {
                sum += Character.getNumericValue(digit);
                rollDigits.append(digit).append("+");
            }
        }

        // Remove the last "+" and show calculation
        if (rollDigits.length() > 0) {
            rollDigits.setLength(rollDigits.length() - 1);
            Toast.makeText(this, "Roll Number Digits: " + rollDigits.toString() + " = " + sum,
                          Toast.LENGTH_LONG).show();
        }

        currentInput = String.valueOf(sum);
        tvDisplay.setText(currentInput);
        isEqualsPressed = true;
        operator = "";
    }
}
