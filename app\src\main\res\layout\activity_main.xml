<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Student Name Header -->
    <TextView
        android:id="@+id/tvStudentName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/student_name"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textColor="@color/primary"
        android:layout_marginBottom="16dp" />

    <!-- Calculator Display -->
    <TextView
        android:id="@+id/tvDisplay"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/display_background"
        android:text="@string/display_hint"
        android:textSize="36sp"
        android:textColor="@color/display_text"
        android:gravity="center_vertical|end"
        android:padding="16dp"
        android:layout_marginBottom="16dp"
        android:fontFamily="monospace" />

    <!-- Button Grid -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="4"
        android:rowCount="6"
        android:layout_gravity="center">

        <!-- Row 1: Clear, Backspace, MyR, Divide -->
        <Button
            android:id="@+id/btnClear"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/clear"
            android:textSize="20sp"
            android:backgroundTint="@color/button_special" />

        <Button
            android:id="@+id/btnBackspace"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/backspace"
            android:textSize="20sp"
            android:backgroundTint="@color/button_special" />

        <Button
            android:id="@+id/btnMyR"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/my_r"
            android:textSize="18sp"
            android:textStyle="bold"
            android:backgroundTint="@color/button_myr" />

        <Button
            android:id="@+id/btnDivide"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/divide"
            android:textSize="20sp"
            android:backgroundTint="@color/button_operator" />

        <!-- Row 2: 7, 8, 9, Multiply -->
        <Button
            android:id="@+id/btn7"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/seven"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn8"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/eight"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn9"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/nine"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btnMultiply"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/multiply"
            android:textSize="20sp"
            android:backgroundTint="@color/button_operator" />

        <!-- Row 3: 4, 5, 6, Minus -->
        <Button
            android:id="@+id/btn4"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/four"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn5"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/five"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn6"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/six"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btnMinus"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/minus"
            android:textSize="20sp"
            android:backgroundTint="@color/button_operator" />

        <!-- Row 4: 1, 2, 3, Plus -->
        <Button
            android:id="@+id/btn1"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/one"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn2"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/two"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btn3"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/three"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btnPlus"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/plus"
            android:textSize="20sp"
            android:backgroundTint="@color/button_operator" />

        <!-- Row 5: 0, Decimal, Equals (spans 2 columns) -->
        <Button
            android:id="@+id/btn0"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/zero"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btnDecimal"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:text="@string/decimal"
            android:textSize="20sp"
            android:backgroundTint="@color/button_number" />

        <Button
            android:id="@+id/btnEquals"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="2"
            android:layout_columnSpan="2"
            android:layout_margin="4dp"
            android:text="@string/equals"
            android:textSize="20sp"
            android:backgroundTint="@color/button_operator" />

    </GridLayout>

</LinearLayout>
