# Android Calculator App

A fully functional calculator app for Android with standard arithmetic operations and a special MyR button.

## Features

### Standard Calculator Functions
- **Basic Operations**: Addition (+), Subtraction (-), Multiplication (×), Division (÷)
- **Number Input**: Digits 0-9 with decimal point support
- **Clear (C)**: Clears all input and resets calculator
- **Backspace (⌫)**: <PERSON><PERSON><PERSON> last entered digit
- **Equals (=)**: Calculates and displays result

### Special Features
- **MyR Button**: Calculates the sum of the first 5 digits of your roll number
- **Student Name Display**: Shows your name at the top of the calculator
- **Error Handling**: Displays "Error" for invalid operations (like division by zero)
- **Toast Messages**: Shows calculation breakdown when MyR button is pressed

## Setup Instructions

### Prerequisites
- Android Studio (latest version recommended)
- Android SDK API Level 24 or higher
- Java 8 or higher

### Installation Steps
1. Open Android Studio
2. Select "Open an Existing Project"
3. Navigate to the calculator app folder and select it
4. Wait for Gradle sync to complete
5. **Important**: Update the following in `MainActivity.java`:
   - Line 18: Replace `"12345"` with your actual roll number
   - Line 30: Replace `"Your Name Here"` with your actual name
6. Build and run the app on an emulator or physical device

## Customization Done

### Visual Enhancements
- **Custom Color Scheme**: 
  - Blue-tinted number buttons (#FFE3F2FD)
  - Orange operator buttons (#FFFF6F00)
  - Green special function buttons (#FF2E7D32)
  - Pink MyR button (#FFE91E63) to make it stand out
  - Dark display background (#FF1A1A1A) with green text (#FF00E676)

### Functional Enhancements
- **Enhanced MyR Function**: Shows a toast message displaying the calculation breakdown
- **Improved Error Handling**: Better handling of edge cases and invalid inputs
- **Decimal Support**: Full decimal number support with proper formatting
- **Memory Management**: Proper state management for continuous calculations

### UI/UX Improvements
- **Responsive Layout**: Uses GridLayout for consistent button sizing
- **Professional Typography**: Monospace font for display, proper text sizing
- **Visual Feedback**: Different colors for different button types
- **Accessibility**: Proper button sizing and contrast ratios

## Screenshots Required for Submission

1. **Home Screen**: Shows your name at the top and all calculator buttons
2. **MyR Button Output**: Shows the result when MyR button is pressed
3. **Error Handling**: Any errors encountered and how they were fixed

## Common Issues and Solutions

### Issue 1: Gradle Sync Failed
**Error**: "Failed to sync Gradle"
**Solution**: 
- Check internet connection
- Update Android Studio to latest version
- Clean and rebuild project (Build → Clean Project → Rebuild Project)

### Issue 2: App Crashes on Launch
**Error**: App stops working when opened
**Solution**:
- Check if roll number and name are properly set in MainActivity.java
- Verify all resource files are present
- Check logcat for specific error messages

### Issue 3: MyR Button Not Working
**Error**: MyR button doesn't show expected result
**Solution**:
- Ensure ROLL_NUMBER constant contains only numeric characters
- Check that roll number has at least 5 digits
- Verify the calculation logic in handleMyRInput() method

## Technical Details

- **Minimum SDK**: API 24 (Android 7.0)
- **Target SDK**: API 34 (Android 14)
- **Language**: Java
- **Architecture**: Single Activity with event-driven UI
- **Layout**: LinearLayout with GridLayout for button arrangement

## File Structure
```
app/
├── src/main/
│   ├── java/com/example/calculatorapp/
│   │   └── MainActivity.java
│   ├── res/
│   │   ├── layout/
│   │   │   └── activity_main.xml
│   │   ├── values/
│   │   │   ├── colors.xml
│   │   │   ├── strings.xml
│   │   │   └── themes.xml
│   │   └── xml/
│   └── AndroidManifest.xml
└── build.gradle
```

## Additional Credits

This calculator app includes several enhancements beyond the basic requirements:
- Professional UI design with custom color scheme
- Enhanced error handling and user feedback
- Toast messages for better user experience
- Responsive layout design
- Comprehensive documentation

---

**Note**: Remember to update your roll number and name in the MainActivity.java file before building the app!
